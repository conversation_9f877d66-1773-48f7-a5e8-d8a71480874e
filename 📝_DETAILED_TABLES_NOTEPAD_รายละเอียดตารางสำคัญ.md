# 📝 DETAILED TABLES NOTEPAD - รายละเอียดตารางสำคัญ

## 🎯 ตารางหลักที่ใช้งานบ่อย

### 1. 🛠️ ตาราง `services` - บริการหลัก

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE services (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,           -- ชื่อบริการ
    description TEXT,                      -- คำอธิบายสั้น
    details TEXT,                          -- รายละเอียดยาว
    image VARCHAR(255),                    -- รูปภาพหลัก
    is_active BOOLEAN DEFAULT 1,          -- สถานะเปิด/ปิด
    sort_order INT DEFAULT 0,             -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **หน้าแรก**: แสดง 6 บริการแบบสุ่ม
- **หน้าบริการ**: แสดงทั้งหมดที่ is_active = 1
- **หน้ารายละเอียด**: แสดงข้อมูลครบถ้วน + รูปภาพ

#### 📊 ข้อมูลตัวอย่าง:
- บริการจัดงานศพแบบไทย
- บริการจัดงานศพแบบจีน  
- บริการรับจัดงานบุญ
- บริการดูแลศพ 24 ชั่วโมง

---

### 2. 🖼️ ตาราง `service_images` - รูปภาพบริการ

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE service_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    service_id BIGINT,                    -- FK ไปยัง services
    image_path VARCHAR(255) NOT NULL,     -- ที่อยู่ไฟล์รูป
    alt_text VARCHAR(255),                -- ข้อความทดแทน
    description TEXT,                     -- คำอธิบายรูป
    is_cover BOOLEAN DEFAULT 0,          -- รูปหน้าปก
    sort_order INT DEFAULT 0,            -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);
```

#### 🔍 การใช้งาน:
- **แกลเลอรี่**: แสดงรูปภาพทั้งหมดของบริการ
- **รูปหน้าปก**: is_cover = 1 (1 รูปต่อบริการ)
- **เรียงลำดับ**: ตาม sort_order

---

### 3. 🎨 ตาราง `activities` - ผลงาน

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,          -- ชื่อผลงาน
    description TEXT,                     -- คำอธิบายสั้น
    details TEXT,                         -- รายละเอียดยาว
    image VARCHAR(255) NULLABLE,          -- รูปหลัก (เลิกใช้แล้ว)
    is_active BOOLEAN DEFAULT 1,         -- สถานะเปิด/ปิด
    sort_order INT DEFAULT 0,            -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **หน้าแรก**: แสดง 4 ผลงานแบบสุ่ม
- **หน้าผลงาน**: แสดงทั้งหมดที่ is_active = 1
- **หน้ารายละเอียด**: แสดงข้อมูล + แกลเลอรี่

#### 📝 หมายเหตุ:
- ฟิลด์ `image` เป็น nullable เพราะใช้ activity_images แทน

---

### 4. 📸 ตาราง `activity_images` - รูปภาพผลงาน

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE activity_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    activity_id BIGINT,                   -- FK ไปยัง activities
    image_path VARCHAR(255) NOT NULL,     -- ที่อยู่ไฟล์รูป
    alt_text VARCHAR(255),                -- ข้อความทดแทน
    description TEXT,                     -- คำอธิบายรูป
    is_cover BOOLEAN DEFAULT 0,          -- รูปหน้าปก
    sort_order INT DEFAULT 0,            -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE
);
```

#### 🔍 การใช้งาน:
- **แกลเลอรี่**: แสดงรูปภาพทั้งหมดของผลงาน
- **รูปหน้าปก**: is_cover = 1 สำหรับแสดงในหน้าหลัก
- **เรียงลำดับ**: ตาม sort_order

---

### 5. 📦 ตาราง `packages` - แพ็คเกจบริการ

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE packages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,          -- ชื่อแพ็คเกจ
    description TEXT,                     -- คำอธิบายสั้น
    details TEXT,                         -- รายละเอียดยาว
    price VARCHAR(255),                   -- ราคา (String)
    image VARCHAR(255),                   -- รูปภาพแพ็คเกจ
    is_active BOOLEAN DEFAULT 1,         -- สถานะเปิด/ปิด
    sort_order INT DEFAULT 0,            -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **หน้าแพ็คเกจ**: แสดงทั้งหมดที่ is_active = 1
- **หน้ารายละเอียด**: แสดงข้อมูลครบถ้วน

#### 📝 หมายเหตุ:
- ฟิลด์ `price` เป็น VARCHAR เพื่อรองรับข้อความ เช่น "ตามตกลง"

---

### 6. 🎯 ตาราง `banners` - แบนเนอร์สไลด์

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE banners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,          -- ชื่อแบนเนอร์
    description TEXT,                     -- คำอธิบาย
    image_path VARCHAR(255) NOT NULL,     -- ที่อยู่ไฟล์รูป
    display_pages JSON,                   -- หน้าที่แสดง
    is_active BOOLEAN DEFAULT 1,         -- สถานะเปิด/ปิด
    sort_order INT DEFAULT 0,            -- ลำดับการแสดง
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **สไลด์โชว์**: แสดงในหน้าที่กำหนดใน display_pages
- **หน้าที่รองรับ**: home, services, packages, activities, contact

#### 📊 ตัวอย่าง display_pages:
```json
["home", "services"]  // แสดงในหน้าแรกและหน้าบริการ
["home"]              // แสดงเฉพาะหน้าแรก
```

---

### 7. 📞 ตาราง `contacts` - ข้อความติดต่อ

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE contacts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,           -- ชื่อผู้ติดต่อ
    email VARCHAR(255) NOT NULL,          -- อีเมล
    phone VARCHAR(20),                    -- เบอร์โทร
    subject VARCHAR(255) NOT NULL,        -- หัวข้อ
    message TEXT NOT NULL,                -- ข้อความ
    is_read BOOLEAN DEFAULT 0,           -- สถานะการอ่าน
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **ฟอร์มติดต่อ**: รับข้อมูลจากลูกค้า
- **ระบบ Admin**: จัดการข้อความ, ทำเครื่องหมายอ่านแล้ว

---

### 8. ⚙️ ตาราง `site_settings` - การตั้งค่า

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE site_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    key VARCHAR(255) UNIQUE NOT NULL,     -- คีย์การตั้งค่า
    value TEXT,                           -- ค่าการตั้งค่า
    description TEXT,                     -- คำอธิบาย
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **ข้อมูลติดต่อ**: เบอร์โทร, อีเมล, ที่อยู่
- **การตั้งค่าเว็บ**: ชื่อเว็บไซต์, คำอธิบาย

#### 📊 ตัวอย่างข้อมูล:
```
site_name = "ผู้ใหญ่ประจักษ์เซอร์วิสช็อป"
phone = "02-xxx-xxxx"
email = "<EMAIL>"
address = "123 ถนน..."
```

---

### 9. 👤 ตาราง `users` - ผู้ดูแลระบบ

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,           -- ชื่อผู้ดูแล
    email VARCHAR(255) UNIQUE NOT NULL,   -- อีเมลเข้าสู่ระบบ
    email_verified_at TIMESTAMP NULL,     -- วันที่ยืนยันอีเมล
    password VARCHAR(255) NOT NULL,       -- รหัสผ่าน (Hash)
    remember_token VARCHAR(100) NULL,     -- โทเค็นจำการเข้าสู่ระบบ
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### 🔍 การใช้งาน:
- **ระบบ Login**: เข้าสู่ระบบ Admin
- **การจัดการ**: CRUD ทุกตาราง
- **Remember Me**: ใช้ remember_token สำหรับจำการเข้าสู่ระบบ

#### 📝 หมายเหตุ:
- ฟิลด์ `email_verified_at` และ `remember_token` เป็น Laravel default
- รหัสผ่านถูกเข้ารหัสด้วย bcrypt

---

### 10. 🗂️ ตาราง `migrations` - ระบบ Laravel

#### 📋 โครงสร้างฟิลด์:
```sql
CREATE TABLE migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    migration VARCHAR(255) NOT NULL,      -- ชื่อไฟล์ migration
    batch INT NOT NULL                    -- กลุ่มการรัน migration
);
```

#### 🔍 การใช้งาน:
- **ติดตามการเปลี่ยนแปลง**: เก็บประวัติการสร้าง/แก้ไขโครงสร้างฐานข้อมูล
- **Version Control**: ระบบ Laravel ใช้ควบคุมเวอร์ชันของฐานข้อมูล
- **Rollback**: สามารถย้อนกลับการเปลี่ยนแปลงได้

#### 📊 ตัวอย่างข้อมูล:
```
id | migration                                    | batch
---|----------------------------------------------|-------
1  | 2014_10_12_000000_create_users_table        | 1
2  | 2025_07_16_181748_create_services_table     | 1
3  | 2025_07_16_181846_create_packages_table     | 1
4  | 2025_07_16_181924_create_contacts_table     | 1
5  | 2025_07_16_181949_create_site_settings_table| 1
```

#### 🔧 คำสั่งที่เกี่ยวข้อง:
```bash
# ดู migration ที่รันแล้ว
php artisan migrate:status

# รัน migration ใหม่
php artisan migrate

# ย้อนกลับ migration
php artisan migrate:rollback

# ย้อนกลับ migration ทั้งหมด
php artisan migrate:reset

# รีเฟรช migration (ลบและสร้างใหม่)
php artisan migrate:refresh
```

#### ⚠️ คำเตือนสำคัญ:
- **ห้ามลบตารางนี้** - เป็นตารางระบบของ Laravel
- **ห้ามแก้ไขข้อมูล** - อาจทำให้ระบบ migration เสียหาย
- **สำรองข้อมูลก่อน** - ก่อนทำการ migrate ในระบบจริง

#### 📈 การอ่านข้อมูล:
- **batch**: หมายเลขกลุ่มการรัน migration พร้อมกัน
- **migration**: ชื่อไฟล์ migration ตามรูปแบบ `YYYY_MM_DD_HHMMSS_description`
- **id**: ลำดับการรัน migration

---

## 🔗 ความสัมพันธ์สำคัญ

### Foreign Key Constraints:
1. `service_images.service_id` → `services.id`
2. `activity_images.activity_id` → `activities.id`

### Cascade Delete:
- ลบ service → ลบ service_images ทั้งหมด
- ลบ activity → ลบ activity_images ทั้งหมด

---

## 📈 Index และ Performance

### Indexes ที่สำคัญ:
- `services.is_active` - กรองบริการที่เปิดใช้
- `activities.is_active` - กรองผลงานที่เปิดใช้
- `banners.is_active` - กรองแบนเนอร์ที่เปิดใช้
- `contacts.is_read` - กรองข้อความที่ยังไม่อ่าน
- `site_settings.key` - ค้นหาการตั้งค่า

### การเรียงลำดับ:
- `sort_order ASC` - เรียงตามลำดับที่กำหนด
- `created_at DESC` - เรียงตามวันที่ล่าสุด

---

**📅 อัปเดตล่าสุด**: 31 กรกฎาคม 2025
**👨‍💻 จัดทำโดย**: Augment Agent
